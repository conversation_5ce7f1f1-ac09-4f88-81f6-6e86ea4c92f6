{"name": "assignment1", "version": "0.0.0", "type": "module", "private": true, "scripts": {"test": "vitest", "test:ui": "vitest --ui", "test:run": "vitest run", "dev": "vite", "build": "tsc && vite build", "generate-pipes": "tsx --tsconfig scripts/tsconfig.json scripts/generate_pipes.ts"}, "dependencies": {"rxjs": "^7.8.2"}, "devDependencies": {"@types/node": "^24.0.10", "@vitest/ui": "^3.2.4", "prettier": "^3.6.2", "tsx": "^4.20.3", "typescript": "~5.8.3", "vite": "^7.0.1", "vite-plugin-checker": "^0.9.3", "vitest": "^3.2.4"}}