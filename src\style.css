:root {
    background-color: rgb(161, 161, 161);
    padding: 2em;
    padding-top: 4em;
}

body,
h2,
h3,
h4,
h5,
h6,
th,
td {
    font-family: arial, helvetica, sans-serif;
}

body {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

h1 {
    font-size: 3em;
    font-family: arial, helvetica, sans-serif;
    font-weight: bolder;
    background-color: rgb(214, 214, 214);
    border-radius: 0.05em;
    padding: 0.1em 0.1em 0.1em 0.1em;
    box-shadow: 0em 0em 0.15em rgb(211, 211, 211);
    text-shadow: 0em 0em 1px rgb(68, 68, 68);
}

.flex {
    display: flex;
    row-gap: 1em;
    column-gap: 1em;
}

.col {
    flex-direction: column;
}

.row {
    flex-direction: row;
}

main {
    background-color: rgb(238, 238, 238);
    border-radius: 0.25em;
    box-shadow: 0em 0em 0.5em rgb(20, 20, 20);
    flex-wrap: wrap;
    padding: 1em;
}

/* https://stackoverflow.com/questions/6088409/svg-drop-shadow-using-css3 */
.shadow {
    -webkit-filter: drop-shadow(0px 0px 0.5px rgba(0, 0, 0, 0.7));
    filter: drop-shadow(0px 0px 0.5px rgba(0, 0, 0, 0.7));
}

svg {
    border-radius: 0.1em;
    border: 5px solid rgb(77, 77, 77);
}

svg rect {
    stroke: black;
    stroke-width: 2px;
}

#svgCanvas {
    background-color: rgb(154, 154, 154);
}

#info {
    width: 160px;
    margin-top: 1em;
}

#info > .text {
    font-weight: bold;
}

.left {
    font-size: 1.1em;
    float: left;
}

.right {
    font-size: 1em;
    float: right;
}

#gameOver {
    font-size: 1.5em;
    font-weight: bolder;
    text-align: center;
}
