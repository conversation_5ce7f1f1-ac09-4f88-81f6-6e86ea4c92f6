{
    "compilerOptions": {
        "target": "ES2024",
        "useDefineForClassFields": true,
        "module": "ESNext",
        "lib": ["ES2024", "DOM", "DOM.Iterable"],
        // Don't include Node.js types
        "types": [],
        "skipLibCheck": true,

        /* Bundler mode */
        "moduleResolution": "bundler",
        "allowImportingTsExtensions": true,
        "resolveJsonModule": true,
        "isolatedModules": true,
        "noEmit": true,

        /* Linting */
        "strict": true,
        // "noUnusedLocals": true,
        // "noUnusedParameters": true,
        // "noFallthroughCasesInSwitch": true,
        "noImplicitAny": true
    },
    "include": ["src", "test"]
}
